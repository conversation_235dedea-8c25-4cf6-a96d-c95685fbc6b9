<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <title inertia>AdonisJS x Inertia x VueJS</title>

  <link rel="preconnect" href="https://fonts.bunny.net">
  <link href="https://fonts.bunny.net/css?family=instrument-sans:400,400i,500,500i,600,600i,700,700i" rel="stylesheet" />

  <style>
    :root {
      --sand-1: #fdfdfc;
      --sand-2: #f9f9f8;
      --sand-3: #f1f0ef;
      --sand-4: #e9e8e6;
      --sand-5: #e2e1de;
      --sand-6: #dad9d6;
      --sand-7: #cfceca;
      --sand-8: #bcbbb5;
      --sand-9: #8d8d86;
      --sand-10: #82827c;
      --sand-11: #63635e;
      --sand-12: #21201c;
    }
  </style>

  <script src="https://cdn.tailwindcss.com"></script>

  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            sans: ['Instrument Sans', 'sans-serif'],
          },
          colors: {
            primary: {
              DEFAULT: '#5A45FF',
            },
            sand: {
              1: 'var(--sand-1)',
              2: 'var(--sand-2)',
              3: 'var(--sand-3)',
              4: 'var(--sand-4)',
              5: 'var(--sand-5)',
              6: 'var(--sand-6)',
              7: 'var(--sand-7)',
              8: 'var(--sand-8)',
              9: 'var(--sand-9)',
              10: 'var(--sand-10)',
              11: 'var(--sand-11)',
              12: 'var(--sand-12)',
            },
          },
        },
      },
    }
  </script>

  @vite(['inertia/app/app.ts', `inertia/pages/${page.component}.vue`])
  @inertiaHead()
  @stack('dumper')
</head>

<body class="min-h-screen w-screen font-sans">
  @inertia()
</body>

</html>